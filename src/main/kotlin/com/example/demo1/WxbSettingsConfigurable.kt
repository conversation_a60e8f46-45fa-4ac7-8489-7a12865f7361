package com.example.demo1

import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.Project
import com.intellij.ui.components.JBCheckBox
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.FormBuilder
import java.awt.BorderLayout
import java.awt.event.ActionEvent
import java.awt.event.ActionListener
import javax.swing.*

/**
 * Configuration UI for WxbCompletionProvider settings
 */
class WxbSettingsConfigurable(private val project: Project) : Configurable {
    
    private var settingsPanel: JPanel? = null
    private var enableDebugCheckBox: JBCheckBox? = null
    private val pathTextFields = mutableListOf<JBTextField>()
    private var pathsPanel: JPanel? = null
    
    override fun getDisplayName(): String = "Wxb Completion"
    
    override fun createComponent(): JComponent? {
        val settings = WxbSettings.getInstance(project)
        
        enableDebugCheckBox = JBCheckBox("Enable debug logging", settings.enableDebugLogging)
        
        // Create paths panel
        pathsPanel = JPanel()
        pathsPanel!!.layout = BoxLayout(pathsPanel, BoxLayout.Y_AXIS)
        updatePathsPanel(settings.languageFilePaths)
        
        val addButton = JButton("Add Path")
        addButton.addActionListener {
            addNewPath("")
        }
        
        val mainPanel = FormBuilder.createFormBuilder()
            .addComponent(JBLabel("Language file paths (relative to solution/project root):"))
            .addComponent(pathsPanel!!)
            .addComponent(addButton)
            .addVerticalGap(10)
            .addComponent(enableDebugCheckBox!!)
            .addComponentFillVertically(JPanel(), 0)
            .panel
            
        settingsPanel = mainPanel
        return settingsPanel
    }
    
    private fun updatePathsPanel(paths: List<String>) {
        pathsPanel?.removeAll()
        pathTextFields.clear()
        
        paths.forEach { path ->
            addPathRow(path)
        }
        
        pathsPanel?.revalidate()
        pathsPanel?.repaint()
    }
    
    private fun addNewPath(path: String) {
        addPathRow(path)
        pathsPanel?.revalidate()
        pathsPanel?.repaint()
    }
    
    private fun addPathRow(path: String) {
        val rowPanel = JPanel(BorderLayout())
        val textField = JBTextField(path, 30)
        val removeButton = JButton("Remove")
        
        removeButton.addActionListener {
            pathTextFields.remove(textField)
            pathsPanel?.remove(rowPanel)
            pathsPanel?.revalidate()
            pathsPanel?.repaint()
        }
        
        rowPanel.add(textField, BorderLayout.CENTER)
        rowPanel.add(removeButton, BorderLayout.EAST)
        
        pathTextFields.add(textField)
        pathsPanel?.add(rowPanel)
    }
    
    override fun isModified(): Boolean {
        val settings = WxbSettings.getInstance(project)
        
        if (enableDebugCheckBox?.isSelected != settings.enableDebugLogging) {
            return true
        }
        
        val currentPaths = pathTextFields.map { it.text.trim() }.filter { it.isNotEmpty() }
        if (currentPaths != settings.languageFilePaths) {
            return true
        }
        
        return false
    }
    
    override fun apply() {
        val settings = WxbSettings.getInstance(project)
        
        settings.enableDebugLogging = enableDebugCheckBox?.isSelected ?: true
        
        val newPaths = pathTextFields.map { it.text.trim() }.filter { it.isNotEmpty() }
        settings.languageFilePaths.clear()
        settings.languageFilePaths.addAll(newPaths)
        
        // Clear cache to force reload with new paths
        WxbCompletionProvider.clearCache()
    }
    
    override fun reset() {
        val settings = WxbSettings.getInstance(project)
        enableDebugCheckBox?.isSelected = settings.enableDebugLogging
        updatePathsPanel(settings.languageFilePaths)
    }
}
