package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.util.TextRange
import com.intellij.patterns.PlatformPatterns
import com.intellij.patterns.StandardPatterns
import com.intellij.psi.PsiElement
import com.intellij.util.ProcessingContext

/**
 * Provides 
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Get the text before cursor
        val element = parameters.position
        val document = parameters.editor.document
        val offset = parameters.offset

        // Get text before cursor (up to 20 characters to check for "setTips(")
        val startOffset = maxOf(0, offset - 20)
        val textBeforeCursor = document.getText(com.intellij.openapi.util.TextRange(startOffset, offset))

        // Only provide completion if "setTips(" is found before cursor
        if (!textBeforeCursor.contains("setTips(")) {
            return
        }

        // Create completion item
        val lookupElement = LookupElementBuilder.create("我的提示")
            .withPresentableText("我的提示")
            .withTypeText("String")
            .withIcon(null)

        // Add to completion results
        result.addElement(lookupElement)
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Register completion provider for all contexts
        // The actual filtering is done in WxbCompletionProvider.addCompletions()
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}