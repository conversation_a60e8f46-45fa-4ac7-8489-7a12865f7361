package com.example.demo1

import com.intellij.openapi.components.*
import com.intellij.openapi.project.Project
import com.intellij.util.xmlb.XmlSerializerUtil

/**
 * Settings for WxbCompletionProvider
 */
@State(
    name = "WxbCompletionSettings",
    storages = [Storage("wxbCompletion.xml")]
)
@Service(Service.Level.PROJECT)
class WxbSettings : PersistentStateComponent<WxbSettings> {
    
    /**
     * List of language file paths to search (relative to solution/project root)
     */
    var languageFilePaths: MutableList<String> = mutableListOf(
        "ConsoleApp2/language.txt",
    )
    
    /**
     * Whether to enable debug logging
     */
    var enableDebugLogging: Boolean = true
    
    override fun getState(): WxbSettings = this
    
    override fun loadState(state: WxbSettings) {
        XmlSerializerUtil.copyBean(state, this)
    }
    
    companion object {
        fun getInstance(project: Project): WxbSettings {
            return project.service<WxbSettings>()
        }
    }
}
