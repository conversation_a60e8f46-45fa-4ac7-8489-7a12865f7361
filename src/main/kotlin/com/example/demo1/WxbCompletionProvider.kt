package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * Language entry data class
 */
data class LanguageEntry(val key: String, val value: String)

/**
 * Provides completion with cached language file reading
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    companion object {
        // Cache for language entries by project path
        private val languageCache = ConcurrentHashMap<String, List<LanguageEntry>>()
        private val fileListeners = ConcurrentHashMap<String, BulkFileListener>()

        fun getLanguageEntries(project: Project): List<LanguageEntry> {
            val searchPath = getProjectSearchPath(project)

            // Check cache first
            languageCache[searchPath]?.let { return it }

            // Load from configured files
            val entries = loadLanguageFile(searchPath, project)
            languageCache[searchPath] = entries

            // Setup file listener if not already done
            if (!fileListeners.containsKey(searchPath)) {
                setupFileListener(project, searchPath)
            }

            return entries
        }

        fun clearCache() {
            languageCache.clear()
        }

        fun initializeFileMonitoring(project: Project) {
            val searchPath = getProjectSearchPath(project)
            val settings = WxbSettings.getInstance(project)

            // Setup file listener immediately when plugin activates
            if (!fileListeners.containsKey(searchPath)) {
                setupFileListener(project, searchPath)

                if (settings.enableDebugLogging) {
                    println("WxbCompletionProvider: File monitoring initialized for project: ${project.name}")
                    println("WxbCompletionProvider: Monitoring search path: $searchPath")
                    println("WxbCompletionProvider: Configured language file paths:")
                    settings.languageFilePaths.forEach { path ->
                        println("  - $path")
                    }
                }
            }
        }

        private fun getProjectSearchPath(project: Project): String {
            val basePath = project.basePath ?: return System.getProperty("user.dir")

            // For Rider, look for .sln file and use its directory
            val baseDir = File(basePath)
            val slnFile = baseDir.listFiles()?.find { it.extension == "sln" }

            return if (slnFile != null) {
                slnFile.parent // Use .sln file's directory
            } else {
                // Fallback: check parent directories for .sln file
                var currentDir = baseDir
                while (currentDir.parent != null) {
                    val parentSlnFile = currentDir.parentFile?.listFiles()?.find { it.extension == "sln" }
                    if (parentSlnFile != null) {
                        return parentSlnFile.parent
                    }
                    currentDir = currentDir.parentFile
                }
                basePath // Final fallback to original basePath
            }
        }

        private fun loadLanguageFile(searchPath: String, project: Project): List<LanguageEntry> {
            val entries = mutableListOf<LanguageEntry>()
            val settings = WxbSettings.getInstance(project)

            // Add a test entry for debugging
            entries.add(LanguageEntry("test", "测试 "))

            try {
                val configuredPaths = settings.languageFilePaths
                val possiblePaths = mutableListOf<File>()

                // Add configured paths relative to search path
                configuredPaths.forEach { relativePath ->
                    possiblePaths.add(File(searchPath, relativePath))
                }

                // Add fallback paths
                possiblePaths.addAll(listOf(
                    File(project.basePath ?: "", "language.txt"),       // Project basePath fallback
                    File(System.getProperty("user.dir"), "language.txt") // Current working directory
                ))

                var loadedFiles = 0
                for (path in possiblePaths) {
                    if (path.exists()) {
                        loadSingleLanguageFile(path, entries, settings.enableDebugLogging)
                        loadedFiles++
                    }
                }

                if(loadedFiles == 0) {
                    Messages.showMessageDialog(
                        project,
                        "未找到语言文件！\n\n请检查以下配置路径是否正确：\n${configuredPaths.joinToString("\n")}\n\n搜索根目录：$searchPath",
                        "Wxb Completion - 语言文件未找到",
                        Messages.getInformationIcon());


                }
            } catch (e: Exception) {
                if (settings.enableDebugLogging) {
                    println("WxbCompletionProvider: Error loading language files: ${e.message}")
                    e.printStackTrace()
                }
            }

            return entries
        }

        private fun loadSingleLanguageFile(file: File, entries: MutableList<LanguageEntry>, enableDebug: Boolean) {
            try {
                val lines = file.readLines()
                var currentId: String? = null
                var currentValue: String? = null
                var prefix = "- _Id:"
                var prefixLen = prefix.length

                for (line in lines) {
                    val trimmedLine = line.trim()

                    // Skip empty lines and comments
                    if (trimmedLine.isEmpty() || trimmedLine.startsWith("#")) {
                        continue
                    }

                    // Parse _Id field
                    if (trimmedLine.startsWith(prefix)) {
                        // Save previous entry if we have both id and value
                        if (currentId != null && currentValue != null) {
                            entries.add(LanguageEntry(currentId, currentValue))
                        }

                        // Extract new id
                        currentId = trimmedLine.substring(prefixLen).trim()
                        currentValue = null // Reset value for new entry
                    }
                    // Parse _Zh_CN field
                    else if (trimmedLine.startsWith("_Zh_CN:")) {
                        val valueText = trimmedLine.substring(8).trim()
                        // Remove quotes and handle unicode escapes
                        currentValue = parseYamlString(valueText)
                    }
                }

                // Don't forget the last entry
                if (currentId != null && currentValue != null) {
                    entries.add(LanguageEntry(currentId, currentValue + " "))
                }

                if (enableDebug) {
                    println("WxbCompletionProvider: Loaded language file: ${file.absolutePath}")
                }
            } catch (e: Exception) {
                if (enableDebug) {
                    println("WxbCompletionProvider: Error loading file ${file.absolutePath}: ${e.message}")
                    e.printStackTrace()
                }
            }
        }

        private fun parseYamlString(yamlValue: String): String {
            var result = yamlValue

            // Remove surrounding quotes
            if ((result.startsWith("\"") && result.endsWith("\"")) ||
                (result.startsWith("'") && result.endsWith("'"))) {
                result = result.substring(1, result.length - 1)
            }

            // Handle unicode escapes like \u5206
            result = result.replace(Regex("\\\\u([0-9a-fA-F]{4})")) { matchResult ->
                val hexCode = matchResult.groupValues[1]
                val codePoint = hexCode.toInt(16)
                codePoint.toChar().toString()
            }

            // Handle other common escapes
            result = result.replace("\\n", "\n")
                          .replace("\\t", "\t")
                          .replace("\\r", "\r")
                          .replace("\\\"", "\"")
                          .replace("\\'", "'")
                          .replace("\\\\", "\\")

            return result
        }

        private fun setupFileListener(project: Project, searchPath: String) {
            val listener = object : BulkFileListener {
                override fun after(events: List<VFileEvent>) {
                    events.forEach { event ->
                        val file = event.file
                        if (file != null && file.name.contains("language")) {
                            // Get current settings to check configured paths
                            val settings = WxbSettings.getInstance(project)
                            val configuredPaths = settings.languageFilePaths

                            // Check if the changed file matches any of our configured paths
                            val fileAbsolutePath = file.path
                            var shouldInvalidateCache = false

                            configuredPaths.forEach { relativePath ->
                                val expectedPath = File(searchPath, relativePath).absolutePath
                                if (fileAbsolutePath == expectedPath) {
                                    shouldInvalidateCache = true
                                }
                            }

                            if (shouldInvalidateCache) {
                                // Clear all cache when any language file changes
                                languageCache.clear()
                                if (settings.enableDebugLogging) {
                                    println("WxbCompletionProvider: Cache cleared due to language file change at: ${file.path}")
                                }
                            }
                        }
                    }
                }
            }

            project.messageBus.connect().subscribe(VirtualFileManager.VFS_CHANGES, listener)
            fileListeners[searchPath] = listener
        }
    }


    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Get the text before cursor
        val element = parameters.position
        val document = parameters.editor.document
        val offset = parameters.offset

        // Get text before cursor (up to 50 characters to check for "setTips(")
        val startOffset = maxOf(0, offset - 50)
        val textBeforeCursor = document.getText(TextRange(startOffset, offset))

        // Only provide completion if "setTips(" is found before cursor
        if (!textBeforeCursor.contains("setTips(")) {
            return
        }

        // Get the current input text (what user has typed after setTips()
        val setTipsIndex = textBeforeCursor.lastIndexOf("setTips(")
        val afterSetTips = textBeforeCursor.substring(setTipsIndex + "setTips(".length)

        // Remove quotes and get the actual input
        val currentInput = afterSetTips.replace("\"", "").replace("'", "").trim()

        // Load language entries from cached file
        val project = parameters.originalFile.project
        val languageEntries = getLanguageEntries(project)
        val settings = WxbSettings.getInstance(project)

        // Debug: print current input
        if (settings.enableDebugLogging && currentInput.isNotEmpty()) {
            println("WxbCompletionProvider: Current input: '$currentInput'")
        }

        // Filter and create completion items based on value matching
        languageEntries.forEach { entry ->
            val (key, value) = entry
            // Check if the value matches the current input (partial or complete match, case insensitive)
            val shouldShow = when {
                currentInput.isEmpty() -> true  // Show all when no input
                value.contains(currentInput, ignoreCase = true) -> true  // Partial match
                value.equals(currentInput, ignoreCase = true) -> true   // Exact match
                else -> false
            }

            if (shouldShow) {
                if (settings.enableDebugLogging && currentInput.isNotEmpty()) {
                    println("WxbCompletionProvider: Showing completion for '$value' (key: $key)")
                }

                val lookupElement = LookupElementBuilder.create(value)
                    .withPresentableText(value)  // Show value in completion popup
                    .withTypeText("→ $key")      // Show corresponding key as type hint
                    .withIcon(null)
                    .withInsertHandler { insertContext, item ->
                        // Replace the current input with the key
                        val insertDocument = insertContext.document
                        val insertStartOffset = insertContext.startOffset
                        val insertEndOffset = insertContext.tailOffset

                        // Calculate where to start replacement (after the quote)
                        val lineStartOffset = insertDocument.getLineStartOffset(insertDocument.getLineNumber(insertEndOffset))
                        val lineText = insertDocument.getText(TextRange(lineStartOffset, insertEndOffset))
                        val setTipsPos = lineText.lastIndexOf("setTips(")

                        if (setTipsPos >= 0) {
                            val quotePos = lineText.indexOf("\"", setTipsPos)
                            if (quotePos >= 0) {
                                val replaceStart = lineStartOffset + quotePos + 1
                                insertDocument.replaceString(replaceStart, insertEndOffset, key)
                            } else {
                                // No quote found, just replace current text
                                insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                            }
                        } else {
                            // Fallback: just replace current text
                            insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                        }
                    }

                result.addElement(lookupElement)
            }
        }
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Register completion provider for all contexts
        // The actual filtering is done in WxbCompletionProvider.addCompletions()
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}