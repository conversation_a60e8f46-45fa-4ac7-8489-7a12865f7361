package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext

/**
 * Provides 我需要在检测到光标前是"setTips(”才提示
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {


        // Create completion item
        val lookupElement = LookupElementBuilder.create("wxb112233")
            .withPresentableText("wxb112233")
            .withTypeText("String")
            .withIcon(null)

        // Add to completion results
        result.addElement(lookupElement)
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {

        var place = PlatformPatterns.psiElement()
        // Provide completion in all contexts
        extend(
            CompletionType.BASIC,
            place,
            WxbCompletionProvider()
        )

    }
}