<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>com.example.demo1</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/best-practices-for-listing.html#plugin-name -->
    <name>Demo1</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor url="https://www.yourcompany.com">YourCompany</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/best-practices-for-listing.html#plugin-description -->
    <description><![CDATA[

    ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <completion.contributor
            language="any"
            implementationClass="com.example.demo1.WxbCompletionContributor"/>

        <projectConfigurable
            parentId="tools"
            instance="com.example.demo1.WxbSettingsConfigurable"
            id="com.example.demo1.WxbSettingsConfigurable"
            displayName="Wxb Completion"/>

        <postStartupActivity
            implementation="com.example.demo1.WxbProjectStartupActivity"/>
    </extensions>
</idea-plugin>