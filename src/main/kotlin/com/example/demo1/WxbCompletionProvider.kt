package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import java.io.File

/**
 * Provides 
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    private fun loadLanguageMap(project: Project): Map<String, String> {
        val languageMap = mutableMapOf<String, String>()

        try {
            // Get project base path
            val basePath = project.basePath ?: return languageMap
            val languageFile = File(basePath, "language.txt")

            if (languageFile.exists()) {
                languageFile.readLines().forEach { line ->
                    val trimmedLine = line.trim()
                    if (trimmedLine.isNotEmpty() && trimmedLine.startsWith("#").not() && trimmedLine.contains("=")) {
                        val parts = trimmedLine.split("=", limit = 2)
                        if (parts.size == 2) {
                            val key = parts[0].trim()
                            val value = parts[1].trim()
                            languageMap[key] = value
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Handle file reading errors silently
        }

        return languageMap
    }

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Get the text before cursor
        val element = parameters.position
        val document = parameters.editor.document
        val offset = parameters.offset

        // Get text before cursor (up to 20 characters to check for "setTips(")
        val startOffset = maxOf(0, offset - 20)
        val textBeforeCursor = document.getText(TextRange(startOffset, offset))

        // Only provide completion if "setTips(" is found before cursor
        if (!textBeforeCursor.contains("setTips(")) {
            return
        }

        // Load language mappings from file
        val project = parameters.originalFile.project
        val languageMap = loadLanguageMap(project)

        // Create completion items for each key-value pair
        languageMap.forEach { (key, value) ->
            val lookupElement = LookupElementBuilder.create(key)
                .withPresentableText(value)  // Show value in completion popup
                .withTypeText("Language Key")
                .withIcon(null)
                .withInsertHandler { insertContext, item ->
                    // Insert the key (not the value) when selected
                    val insertDocument = insertContext.document
                    val insertStartOffset = insertContext.startOffset
                    val insertEndOffset = insertContext.tailOffset
                    insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                }

            result.addElement(lookupElement)
        }
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Register completion provider for all contexts
        // The actual filtering is done in WxbCompletionProvider.addCompletions()
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}