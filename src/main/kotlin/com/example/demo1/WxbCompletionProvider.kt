package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * Language entry data class
 */
data class LanguageEntry(val key: String, val value: String)

/**
 * Provides completion with cached language file reading
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    companion object {
        // Cache for language entries by project path
        private val languageCache = ConcurrentHashMap<String, List<LanguageEntry>>()
        private val fileListeners = ConcurrentHashMap<String, BulkFileListener>()

        fun getLanguageEntries(project: Project): List<LanguageEntry> {
            val searchPath = getProjectSearchPath(project)

            // Check cache first
            languageCache[searchPath]?.let { return it }

            // Load from configured files
            val entries = loadLanguageFile(searchPath, project)
            languageCache[searchPath] = entries

            // Setup file listener if not already done
            if (!fileListeners.containsKey(searchPath)) {
                setupFileListener(project, searchPath)
            }

            return entries
        }

        fun clearCache() {
            languageCache.clear()
        }

        private fun getProjectSearchPath(project: Project): String {
            val basePath = project.basePath ?: return System.getProperty("user.dir")

            // For Rider, look for .sln file and use its directory
            val baseDir = File(basePath)
            val slnFile = baseDir.listFiles()?.find { it.extension == "sln" }

            return if (slnFile != null) {
                slnFile.parent // Use .sln file's directory
            } else {
                // Fallback: check parent directories for .sln file
                var currentDir = baseDir
                while (currentDir.parent != null) {
                    val parentSlnFile = currentDir.parentFile?.listFiles()?.find { it.extension == "sln" }
                    if (parentSlnFile != null) {
                        return parentSlnFile.parent
                    }
                    currentDir = currentDir.parentFile
                }
                basePath // Final fallback to original basePath
            }
        }

        private fun loadLanguageFile(searchPath: String, project: Project): List<LanguageEntry> {
            val entries = mutableListOf<LanguageEntry>()
            val settings = WxbSettings.getInstance(project)

            // Add a test entry for debugging
            entries.add(LanguageEntry("test", "测试"))

            try {
                val configuredPaths = settings.languageFilePaths
                val possiblePaths = mutableListOf<File>()

                // Add configured paths relative to search path
                configuredPaths.forEach { relativePath ->
                    possiblePaths.add(File(searchPath, relativePath))
                }

                // Add fallback paths
                possiblePaths.addAll(listOf(
                    File(project.basePath ?: "", "language.txt"),       // Project basePath fallback
                    File(System.getProperty("user.dir"), "language.txt") // Current working directory
                ))

                var loadedFiles = 0
                for (path in possiblePaths) {
                    if (path.exists()) {
                        loadSingleLanguageFile(path, entries, settings.enableDebugLogging)
                        loadedFiles++
                    }
                }

                if (settings.enableDebugLogging) {
                    if (loadedFiles > 0) {
                        println("WxbCompletionProvider: Loaded $loadedFiles language files")
                        println("WxbCompletionProvider: Solution path: $searchPath")
                        println("WxbCompletionProvider: Project basePath: ${project.basePath}")
                    } else {
                        println("WxbCompletionProvider: No language files found. Solution path: $searchPath")
                        println("WxbCompletionProvider: Project basePath: ${project.basePath}")
                        println("WxbCompletionProvider: Configured paths:")
                        configuredPaths.forEach { println("  - $it") }
                        println("WxbCompletionProvider: Searched in:")
                        possiblePaths.forEach { println("  - ${it.absolutePath} (exists: ${it.exists()})") }
                    }
                }
            } catch (e: Exception) {
                if (settings.enableDebugLogging) {
                    println("WxbCompletionProvider: Error loading language files: ${e.message}")
                    e.printStackTrace()
                }
            }

            return entries
        }

        private fun loadSingleLanguageFile(file: File, entries: MutableList<LanguageEntry>, enableDebug: Boolean) {
            try {
                file.readLines().forEach { line ->
                    val trimmedLine = line.trim()
                    if (trimmedLine.isNotEmpty() && !trimmedLine.startsWith("#") && trimmedLine.contains("=")) {
                        val parts = trimmedLine.split("=", limit = 2)
                        if (parts.size == 2) {
                            val key = parts[0].trim()
                            val value = parts[1].trim()
                            entries.add(LanguageEntry(key, value))
                        }
                    }
                }

                if (enableDebug) {
                    println("WxbCompletionProvider: Loaded language file: ${file.absolutePath}")
                }
            } catch (e: Exception) {
                if (enableDebug) {
                    println("WxbCompletionProvider: Error loading file ${file.absolutePath}: ${e.message}")
                }
            }
        }

        private fun setupFileListener(project: Project, searchPath: String) {
            val listener = object : BulkFileListener {
                override fun after(events: List<VFileEvent>) {
                    events.forEach { event ->
                        val file = event.file
                        if (file != null && file.name == "language.txt") {
                            // Check if the file is in any of our search paths
                            val filePath = file.parent?.path
                            if (filePath != null && (filePath == searchPath || filePath.startsWith(searchPath))) {
                                // Invalidate cache when language.txt changes
                                languageCache.remove(searchPath)
                                println("WxbCompletionProvider: Cache invalidated for language.txt change at: ${file.path}")
                            }
                        }
                    }
                }
            }

            project.messageBus.connect().subscribe(VirtualFileManager.VFS_CHANGES, listener)
            fileListeners[searchPath] = listener
        }
    }


    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Get the text before cursor
        val element = parameters.position
        val document = parameters.editor.document
        val offset = parameters.offset

        // Get text before cursor (up to 50 characters to check for "setTips(")
        val startOffset = maxOf(0, offset - 50)
        val textBeforeCursor = document.getText(TextRange(startOffset, offset))

        // Only provide completion if "setTips(" is found before cursor
        if (!textBeforeCursor.contains("setTips(")) {
            return
        }

        // Get the current input text (what user has typed after setTips()
        val setTipsIndex = textBeforeCursor.lastIndexOf("setTips(")
        val afterSetTips = textBeforeCursor.substring(setTipsIndex + "setTips(".length)

        // Remove quotes and get the actual input
        val currentInput = afterSetTips.replace("\"", "").replace("'", "").trim()

        // Load language entries from cached file
        val project = parameters.originalFile.project
        val languageEntries = getLanguageEntries(project)

        // Filter and create completion items based on value matching
        languageEntries.forEach { entry ->
            val (key, value) = entry
            // Check if the value contains the current input (case insensitive)
            if (currentInput.isEmpty() || value.contains(currentInput, ignoreCase = true)) {
                val lookupElement = LookupElementBuilder.create(value)
                    .withPresentableText(value)  // Show value in completion popup
                    .withTypeText("→ $key")      // Show corresponding key as type hint
                    .withIcon(null)
                    .withInsertHandler { insertContext, item ->
                        // Replace the current input with the key
                        val insertDocument = insertContext.document
                        val insertStartOffset = insertContext.startOffset
                        val insertEndOffset = insertContext.tailOffset

                        // Calculate where to start replacement (after the quote)
                        val lineStartOffset = insertDocument.getLineStartOffset(insertDocument.getLineNumber(insertEndOffset))
                        val lineText = insertDocument.getText(TextRange(lineStartOffset, insertEndOffset))
                        val setTipsPos = lineText.lastIndexOf("setTips(")

                        if (setTipsPos >= 0) {
                            val quotePos = lineText.indexOf("\"", setTipsPos)
                            if (quotePos >= 0) {
                                val replaceStart = lineStartOffset + quotePos + 1
                                insertDocument.replaceString(replaceStart, insertEndOffset, key)
                            } else {
                                // No quote found, just replace current text
                                insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                            }
                        } else {
                            // Fallback: just replace current text
                            insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                        }
                    }

                result.addElement(lookupElement)
            }
        }
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Register completion provider for all contexts
        // The actual filtering is done in WxbCompletionProvider.addCompletions()
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}