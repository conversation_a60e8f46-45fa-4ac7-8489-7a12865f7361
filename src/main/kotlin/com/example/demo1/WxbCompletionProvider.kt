package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import java.io.File

/**
 * Provides 
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    private fun loadLanguageMap(project: Project): Map<String, String> {
        val languageMap = mutableMapOf<String, String>()

        try {
            // Get project base path
            val basePath = project.basePath ?: return languageMap
            val languageFile = File(basePath, "language.txt")

            if (languageFile.exists()) {
                languageFile.readLines().forEach { line ->
                    val trimmedLine = line.trim()
                    if (trimmedLine.isNotEmpty() && trimmedLine.startsWith("#").not() && trimmedLine.contains("=")) {
                        val parts = trimmedLine.split("=", limit = 2)
                        if (parts.size == 2) {
                            val key = parts[0].trim()
                            val value = parts[1].trim()
                            languageMap[key] = value
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Handle file reading errors silently
        }

        return languageMap
    }

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Get the text before cursor
        val element = parameters.position
        val document = parameters.editor.document
        val offset = parameters.offset

        // Get text before cursor (up to 50 characters to check for "setTips(")
        val startOffset = maxOf(0, offset - 50)
        val textBeforeCursor = document.getText(TextRange(startOffset, offset))

        // Only provide completion if "setTips(" is found before cursor
        if (!textBeforeCursor.contains("setTips(")) {
            return
        }

        // Get the current input text (what user has typed after setTips()
        val setTipsIndex = textBeforeCursor.lastIndexOf("setTips(")
        val afterSetTips = textBeforeCursor.substring(setTipsIndex + "setTips(".length)

        // Remove quotes and get the actual input
        val currentInput = afterSetTips.replace("\"", "").replace("'", "").trim()

        // Load language mappings from file
        val project = parameters.originalFile.project
        val languageMap = loadLanguageMap(project)

        // Filter and create completion items based on value matching
        languageMap.forEach { (key, value) ->
            // Check if the value contains the current input (case insensitive)
            if (currentInput.isEmpty() || value.contains(currentInput, ignoreCase = true)) {
                val lookupElement = LookupElementBuilder.create(value)
                    .withPresentableText(value)  // Show value in completion popup
                    .withTypeText("→ $key")      // Show corresponding key as type hint
                    .withIcon(null)
                    .withInsertHandler { insertContext, item ->
                        // Replace the current input with the key
                        val insertDocument = insertContext.document
                        val insertStartOffset = insertContext.startOffset
                        val insertEndOffset = insertContext.tailOffset

                        // Calculate where to start replacement (after the quote)
                        val lineStartOffset = insertDocument.getLineStartOffset(insertDocument.getLineNumber(insertEndOffset))
                        val lineText = insertDocument.getText(TextRange(lineStartOffset, insertEndOffset))
                        val setTipsPos = lineText.lastIndexOf("setTips(")

                        if (setTipsPos >= 0) {
                            val quotePos = lineText.indexOf("\"", setTipsPos)
                            if (quotePos >= 0) {
                                val replaceStart = lineStartOffset + quotePos + 1
                                insertDocument.replaceString(replaceStart, insertEndOffset, key)
                            } else {
                                // No quote found, just replace current text
                                insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                            }
                        } else {
                            // Fallback: just replace current text
                            insertDocument.replaceString(insertStartOffset, insertEndOffset, key)
                        }
                    }

                result.addElement(lookupElement)
            }
        }
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Register completion provider for all contexts
        // The actual filtering is done in WxbCompletionProvider.addCompletions()
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}